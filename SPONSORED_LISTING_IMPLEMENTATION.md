# Sponsored Listing Filter Implementation

## Overview
This document outlines the implementation of sponsored listing filters for the admin listings API routes in the eBadollar backend.

## Changes Made

### 1. Updated Admin Listings Endpoint (`listing.js`)

#### Added Sponsored Filter Parameter
- Added `sponsoredFilter` parameter extraction from query string
- Supports values: `"sponsored"`, `"not_sponsored"`, or empty string (all)

#### Enhanced Database Queries
- **Count Query**: Added LEFT JOIN with `ebadollar_sponsored_listing` table
- **Main Query**: Added sponsored status calculation and sponsored end date
- **Status Counts**: Added separate query to calculate sponsored vs non-sponsored counts

#### Updated Response Format
- Added `is_sponsored` boolean field to each listing
- Added `sponsored_end_date` field for active sponsorships
- Added `sponsored` and `not_sponsored` counts to status_counts object

### 2. Added Admin Promote Listing Endpoint (`promotion.js`)

#### New Endpoint: `/v2/api/ebadollar/custom/admin/promote-listing`
- **Method**: POST
- **Permission**: admin|super_admin
- **Body Parameters**:
  - `listing_id` (Number): The listing ID to promote
  - `days` (Number): Number of days to promote

#### Features
- Validates listing exists and is active
- Checks for existing active promotions
- Creates sponsored listing record with special payment method "admin_promotion"
- No balance deduction (admin privilege)
- Returns detailed promotion information

## Database Schema Requirements

### Sponsored Listing Table
The implementation assumes the following table structure:
```sql
ebadollar_sponsored_listing (
  id INT PRIMARY KEY,
  listing_id INT,
  start_date DATETIME,
  end_date DATETIME,
  status VARCHAR(50),
  cost DECIMAL(10,2),
  payment_method VARCHAR(50),
  duration_days INT,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)
```

## API Usage Examples

### 1. Get All Listings with Sponsored Filter
```javascript
// Get only sponsored listings
GET /v2/api/ebadollar/custom/admin/listings/1/10?sponsored=sponsored

// Get only non-sponsored listings  
GET /v2/api/ebadollar/custom/admin/listings/1/10?sponsored=not_sponsored

// Get all listings (default)
GET /v2/api/ebadollar/custom/admin/listings/1/10
```

### 2. Promote a Listing (Admin)
```javascript
POST /v2/api/ebadollar/custom/admin/promote-listing
Content-Type: application/json

{
  "listing_id": 123,
  "days": 7
}
```

## Response Format Changes

### Enhanced Listing Object
```json
{
  "id": 123,
  "name": "Sample Listing",
  "seller": "John Doe",
  "price": "eBa$ 50.00",
  "status": "active",
  "is_sponsored": true,
  "sponsored_end_date": "2024-08-05T10:30:00.000Z"
}
```

### Enhanced Status Counts
```json
{
  "status_counts": {
    "all": 150,
    "active": 120,
    "sold": 20,
    "expired": 10,
    "reported": 0,
    "sponsored": 25,
    "not_sponsored": 125
  }
}
```

## Testing Instructions

### 1. Test Sponsored Filter
1. Create some test listings
2. Promote a few listings using the admin promote endpoint
3. Test the filter endpoints:
   - `/admin/listings/1/10?sponsored=sponsored` - should return only promoted listings
   - `/admin/listings/1/10?sponsored=not_sponsored` - should return non-promoted listings
   - `/admin/listings/1/10` - should return all listings with sponsored status

### 2. Test Admin Promote Endpoint
1. Get an active listing ID
2. Send POST request to promote endpoint
3. Verify the listing appears as sponsored in the listings API
4. Check the sponsored_listing table for the new record

### 3. Frontend Integration
1. The frontend should now receive sponsored status for each listing
2. Sponsored filter buttons should work correctly
3. Status counts should display sponsored/not_sponsored numbers

## Error Handling

### Common Error Responses
- **400**: Invalid parameters or listing already sponsored
- **404**: Listing not found
- **500**: Internal server error

### Validation Rules
- Only active listings can be promoted
- Listings cannot be promoted if already sponsored
- Days parameter must be positive number
- Admin can exceed normal duration limits (with warning)

## Security Considerations
- Admin promote endpoint requires admin or super_admin role
- No balance deduction for admin promotions
- Special payment method "admin_promotion" for tracking
- Proper input validation and SQL injection prevention

## Future Enhancements
1. Add bulk promotion functionality
2. Add promotion scheduling (start date in future)
3. Add promotion cost override for admin
4. Add promotion history tracking
5. Add automatic expiration handling
