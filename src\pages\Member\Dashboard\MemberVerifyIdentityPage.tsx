import { useState, useRef, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { InteractiveButton } from "@/components/InteractiveButton";
import { useToast } from "@/hooks/useToast";
import { LazyLoad } from "@/components/LazyLoad";
import { useSDK } from "@/hooks/useSDK";
import { CameraToUpload } from "@/components/CameraToUpload";
// @ts-ignore
import { ReactMic } from "react-mic";

const MemberVerifyIdentityPage = () => {
  const { success, error: showError } = useToast();
  const navigate = useNavigate();
  const { sdk } = useSDK();

  const [frontIdFile, setFrontIdFile] = useState<File | null>(null);
  const [backIdFile, setBackIdFile] = useState<File | null>(null);
  const [selfieFile, setSelfieFile] = useState<File | null>(null);
  const [voiceFile, setVoiceFile] = useState<File | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [uploadingFiles, setUploadingFiles] = useState<{
    [key: string]: boolean;
  }>({});
  const [verificationStatus, setVerificationStatus] = useState<any>(null);
  const [requirements, setRequirements] = useState<any>(null);

  // Add state for react-mic
  const [recordedVoice, setRecordedVoice] = useState<Blob | null>(null);
  const [isVoiceRecording, setIsVoiceRecording] = useState(false);

  const frontIdInputRef = useRef<HTMLInputElement>(null);
  const backIdInputRef = useRef<HTMLInputElement>(null);
  const selfieInputRef = useRef<HTMLInputElement>(null);
  const voiceInputRef = useRef<HTMLInputElement>(null);

  const loadVerificationData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Load verification status and requirements in parallel
      const [statusResponse, requirementsResponse] = await Promise.all([
        sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/verification/status",
          method: "GET",
        }),
        sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/verification/requirements",
          method: "GET",
        }),
      ]);

      console.log("Verification Status API Response:", statusResponse);
      console.log(
        "Verification Requirements API Response:",
        requirementsResponse
      );

      if (!statusResponse.error) {
        setVerificationStatus(statusResponse.data);

        // Set existing files from status
        const documents = statusResponse.data?.documents || {};
        if (documents.government_id_front) {
          // Create a mock file object for display purposes
          setFrontIdFile(new File([], "government_id_front.jpg"));
        }
        if (documents.government_id_back) {
          setBackIdFile(new File([], "government_id_back.jpg"));
        }
        if (documents.selfie_verification) {
          setSelfieFile(new File([], "selfie_verification.jpg"));
        }
        if (documents.voice_verification) {
          setVoiceFile(new File([], "voice_verification.mp3"));
        }
      } else {
        setError(
          String(statusResponse.message || "Failed to load verification status")
        );
      }

      if (!requirementsResponse.error) {
        setRequirements(requirementsResponse.data);
      } else if (!statusResponse.error) {
        // Only set error if status also failed
        setError(
          String(
            requirementsResponse.message ||
              "Failed to load verification requirements"
          )
        );
      }
    } catch (error: any) {
      console.error("Error loading verification data:", error);
      setError(String(error?.message || "Failed to load verification data"));
    } finally {
      setLoading(false);
    }
  }, [sdk]);

  const handleFileUpload = useCallback(
    async (
      event: React.ChangeEvent<HTMLInputElement>,
      type: "front" | "back" | "selfie"
    ) => {
      const file = event.target.files?.[0];
      if (!file) return;

      // Validate file type
      const validImageTypes = ["image/jpeg", "image/jpg", "image/png"];
      if (!validImageTypes.includes(file.type)) {
        showError("Please upload a JPG or PNG image");
        return;
      }

      // Validate file size (10MB)
      if (file.size > 10 * 1024 * 1024) {
        showError("File size must be less than 10MB");
        return;
      }

      const documentTypeMap = {
        front: "government_id_front",
        back: "government_id_back",
        selfie: "selfie_verification",
      };

      const documentType = documentTypeMap[type];

      try {
        setUploadingFiles((prev) => ({ ...prev, [type]: true }));

        const formData = new FormData();
        formData.append("file", file);
        formData.append("document_type", documentType);

        const response = await sdk.request({
          endpoint:
            "/v2/api/ebadollar/custom/member/verification/upload-document",
          method: "POST",
          body: formData,
        });

        console.log("Upload Document API Response:", response);

        if (!response.error) {
          // Update local state
          switch (type) {
            case "front":
              setFrontIdFile(file);
              break;
            case "back":
              setBackIdFile(file);
              break;
            case "selfie":
              setSelfieFile(file);
              break;
          }

          success("Document uploaded successfully");

          // Refresh verification status
          await loadVerificationData();
        } else {
          showError(String(response.message || "Failed to upload document"));
        }
      } catch (error: any) {
        console.error("Error uploading document:", error);
        showError(String(error?.message || "Failed to upload document"));
      } finally {
        setUploadingFiles((prev) => ({ ...prev, [type]: false }));
      }
    },
    [success, showError, loadVerificationData]
  );

  const handleStartCamera = useCallback(() => {
    // In a real implementation, this would open camera
    showError("Camera functionality would be implemented here");
  }, [showError]);

  const handleStartRecording = useCallback(() => {
    if (isRecording) {
      // Stop recording
      setIsRecording(false);
      // In a real implementation, this would stop recording and save the file
      success("Recording stopped");
    } else {
      // Start recording
      setIsRecording(true);
      success("Recording started");
    }
  }, [isRecording, success]);

  const handleVoiceFileUpload = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      // Validate file type
      const validAudioTypes = [
        "audio/mpeg",
        "audio/wav",
        "audio/mp3",
        "audio/ogg",
      ];
      if (!validAudioTypes.includes(file.type)) {
        showError("Please upload an audio file (MP3, WAV, OGG)");
        return;
      }

      // Validate file size (10MB)
      if (file.size > 10 * 1024 * 1024) {
        showError("File size must be less than 10MB");
        return;
      }

      try {
        setUploadingFiles((prev) => ({ ...prev, voice: true }));

        const formData = new FormData();
        formData.append("file", file);
        formData.append("document_type", "voice_verification");

        const response = await sdk.request({
          endpoint:
            "/v2/api/ebadollar/custom/member/verification/upload-document",
          method: "POST",
          body: formData,
        });

        console.log("Upload Voice API Response:", response);

        if (!response.error) {
          setVoiceFile(file);
          success("Voice recording uploaded successfully");

          // Refresh verification status
          await loadVerificationData();
        } else {
          showError(
            String(response.message || "Failed to upload voice recording")
          );
        }
      } catch (error: any) {
        console.error("Error uploading voice recording:", error);
        showError(String(error?.message || "Failed to upload voice recording"));
      } finally {
        setUploadingFiles((prev) => ({ ...prev, voice: false }));
      }
    },
    [success, showError, loadVerificationData]
  );

  // Add handler for react-mic stop
  const onVoiceStop = useCallback(
    async (recordedData: any) => {
      if (!recordedData.blob) {
        showError("No voice recording data received");
        return;
      }

      setRecordedVoice(recordedData.blob);
      setUploadingFiles((prev) => ({ ...prev, voice: true }));

      try {
        const formData = new FormData();
        formData.append("file", recordedData.blob, "voice_verification.webm");
        formData.append("document_type", "voice_verification");

        const response = await sdk.request({
          endpoint:
            "/v2/api/ebadollar/custom/member/verification/upload-document",
          method: "POST",
          body: formData,
        });

        console.log("Upload Recorded Voice API Response:", response);

        if (!response.error) {
          setVoiceFile(
            new File([recordedData.blob], "voice_verification.webm")
          );
          success("Voice recording uploaded successfully");
          await loadVerificationData();
        } else {
          showError(
            String(response.message || "Failed to upload voice recording")
          );
        }
      } catch (error: any) {
        console.error("Error uploading recorded voice:", error);
        showError(String(error?.message || "Failed to upload voice recording"));
      } finally {
        setUploadingFiles((prev) => ({ ...prev, voice: false }));
      }
    },
    [success, showError, loadVerificationData, sdk]
  );

  const handleCompleteVerification = useCallback(async () => {
    try {
      setSubmitLoading(true);

      // Check if required documents are uploaded
      if (!frontIdFile || !backIdFile || !selfieFile) {
        showError(
          "Please upload all required documents (Front ID, Back ID, and Selfie)"
        );
        return;
      }

      // Submit verification for review
      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/verification/submit",
        method: "POST",
      });

      console.log("Submit Verification API Response:", response);

      if (!response.error) {
        success(
          "Verification submitted successfully! We'll review your documents within 24 hours."
        );
        navigate("/member/dashboard");
      } else {
        showError(String(response.message || "Failed to submit verification"));
      }
    } catch (error: any) {
      console.error("Error submitting verification:", error);
      showError(String(error?.message || "Verification submission failed"));
    } finally {
      setSubmitLoading(false);
    }
  }, [frontIdFile, backIdFile, selfieFile, success]);

  const handleSkipForNow = () => {
    navigate("/member/dashboard");
  };

  const handleBack = useCallback(() => {
    navigate("/member/dashboard");
  }, [navigate]);

  // Load verification status and requirements on mount
  useEffect(() => {
    loadVerificationData();
  }, []);

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#E63946] mx-auto mb-4"></div>
          <p className="text-gray-600">Loading verification data...</p>
        </div>
      </div>
    );
  }

  if (error && !verificationStatus) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="text-4xl mb-4">⚠️</div>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={loadVerificationData}
            className="px-4 py-2 bg-[#E63946] text-white rounded hover:bg-opacity-90"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <main className="flex h-screen">
      {/* Left Sidebar */}
      <div className="w-[250px] bg-[#0F2C59] flex flex-col">
        {/* Logo */}
        <div className="p-6 border-b border-gray-600">
          <h1 className="text-2xl font-bold">
            <span style={{ color: "#E63946" }}>eBa</span>
            <span className="text-white">Dollar</span>
          </h1>
        </div>

        {/* Progress Steps */}
        <div className="p-6 flex-1">
          <div className="space-y-6">
            {/* Basic Information Step - Completed */}
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 rounded-full bg-green-500 flex items-center justify-center">
                <span className="text-white text-xs font-bold">✓</span>
              </div>
              <div>
                <div className="text-white font-medium">Basic Information</div>
              </div>
            </div>

            {/* Verify Identity Step - Current */}
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 rounded-full bg-[#E63946] flex items-center justify-center">
                <span className="text-white text-xs font-bold">2</span>
              </div>
              <div>
                <div className="text-white font-medium">Verify Identity</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Content Area */}
      <div className="flex-1 bg-white p-8 h-screen overflow-y-auto">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Verify your identity
            </h2>
            <p className="text-gray-600">
              Complete the verification process to secure your account and
              unlock all features.
            </p>
          </div>

          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-[#E63946]">
                Identity Verification
              </span>
              <span className="text-sm text-[#E63946]">100% Complete</span>
            </div>
            <div className="flex items-center space-x-2 mb-2">
              <span className="text-xs text-gray-600">Step 2 of 2</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-[#E63946] h-2 rounded-full"
                style={{ width: "100%" }}
              ></div>
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Basic Info</span>
              <span>Verify Identity</span>
            </div>
          </div>

          <div className="space-y-8">
            {/* ID Document Verification */}
            <div className="bg-gray-50 rounded-lg p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                  <span className="text-white text-sm">📄</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    ID Document Verification
                  </h3>
                  <p className="text-sm text-gray-600">
                    Upload clear photos of your government-issued ID
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Front of ID */}
                <div className="text-center">
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 mb-4 hover:border-blue-500 cursor-pointer">
                    <div className="text-4xl text-gray-400 mb-2">🆔</div>
                    <h4 className="font-medium text-gray-900 mb-1">
                      Front of ID
                    </h4>
                    <p className="text-sm text-gray-600 mb-4">
                      Upload the front side of your ID document
                    </p>
                    {frontIdFile && (
                      <p className="text-sm text-green-600 mb-2">
                        ✓ {frontIdFile.name}
                      </p>
                    )}
                  </div>
                  <InteractiveButton
                    className="!bg-blue-500 hover:!bg-blue-600 !text-white !px-6 !py-2"
                    onClick={() => frontIdInputRef.current?.click()}
                    disabled={uploadingFiles.front}
                  >
                    {uploadingFiles.front ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
                        Uploading...
                      </>
                    ) : (
                      "📷 Take Photo / Upload"
                    )}
                  </InteractiveButton>
                  <input
                    ref={frontIdInputRef}
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleFileUpload(e, "front")}
                    className="hidden"
                  />
                  <p className="text-xs text-gray-500 mt-2">
                    JPG, PNG up to 10MB
                  </p>
                </div>

                {/* Back of ID */}
                <div className="text-center">
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 mb-4 hover:border-blue-500 cursor-pointer">
                    <div className="text-4xl text-gray-400 mb-2">🆔</div>
                    <h4 className="font-medium text-gray-900 mb-1">
                      Back of ID
                    </h4>
                    <p className="text-sm text-gray-600 mb-4">
                      Upload the back side of your ID document
                    </p>
                    {backIdFile && (
                      <p className="text-sm text-green-600 mb-2">
                        ✓ {backIdFile.name}
                      </p>
                    )}
                  </div>
                  <InteractiveButton
                    className="!bg-blue-500 hover:!bg-blue-600 !text-white !px-6 !py-2"
                    onClick={() => backIdInputRef.current?.click()}
                    disabled={uploadingFiles.back}
                  >
                    {uploadingFiles.back ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
                        Uploading...
                      </>
                    ) : (
                      "📷 Take Photo / Upload"
                    )}
                  </InteractiveButton>
                  <input
                    ref={backIdInputRef}
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleFileUpload(e, "back")}
                    className="hidden"
                  />
                  <p className="text-xs text-gray-500 mt-2">
                    JPG, PNG up to 10MB
                  </p>
                </div>
              </div>

              <div className="bg-blue-50 rounded-md p-3 mt-4">
                <div className="flex items-start space-x-2">
                  <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center mt-0.5">
                    <span className="text-white text-xs">ℹ</span>
                  </div>
                  <p className="text-xs text-blue-800">
                    <strong>Accepted documents:</strong> Driver's license,
                    passport, national ID card, or state-issued ID
                  </p>
                </div>
              </div>
            </div>

            {/* Selfie Verification */}
            <div className="bg-green-50 rounded-lg p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                  <span className="text-white text-sm">📷</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    Selfie Verification
                  </h3>
                  <p className="text-sm text-gray-600">
                    Take a selfie to verify your identity matches your ID
                  </p>
                </div>
              </div>
              <div className="text-center">
                <div className="mb-4">
                  <CameraToUpload
                    uploadSuccess={async (results: string[]) => {
                      if (results && results.length > 0) {
                        // Upload to the correct API endpoint
                        setUploadingFiles((prev) => ({
                          ...prev,
                          selfie: true,
                        }));
                        try {
                          const formData = new FormData();
                          const blob = await fetch(results[0]).then((r) =>
                            r.blob()
                          );
                          formData.append(
                            "file",
                            blob,
                            "selfie_verification.jpg"
                          );
                          formData.append(
                            "document_type",
                            "selfie_verification"
                          );

                          const response = await sdk.request({
                            endpoint:
                              "/v2/api/ebadollar/custom/member/verification/upload-document",
                            method: "POST",
                            body: formData,
                          });

                          console.log(
                            "Upload Selfie Camera API Response:",
                            response
                          );

                          if (!response.error) {
                            setSelfieFile(
                              new File([blob], "selfie_verification.jpg")
                            );
                            success("Selfie uploaded successfully");
                            await loadVerificationData();
                          } else {
                            showError(
                              String(
                                response.message || "Failed to upload selfie"
                              )
                            );
                          }
                        } catch (error: any) {
                          console.error("Error uploading selfie:", error);
                          showError(
                            String(error?.message || "Failed to upload selfie")
                          );
                        } finally {
                          setUploadingFiles((prev) => ({
                            ...prev,
                            selfie: false,
                          }));
                        }
                      }
                    }}
                  />
                </div>
                <div className="text-sm text-gray-500 my-4">or</div>
                <InteractiveButton
                  className="!bg-blue-500 hover:!bg-blue-600 !text-white !px-6 !py-2"
                  onClick={() => selfieInputRef.current?.click()}
                  disabled={uploadingFiles.selfie}
                >
                  {uploadingFiles.selfie ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
                      Uploading...
                    </>
                  ) : (
                    "📁 Upload Selfie File"
                  )}
                </InteractiveButton>
                <input
                  ref={selfieInputRef}
                  type="file"
                  accept="image/*"
                  onChange={(e) => handleFileUpload(e, "selfie")}
                  className="hidden"
                />
                <p className="text-xs text-gray-500 mt-2">
                  JPG, PNG up to 10MB
                </p>
                {selfieFile && (
                  <p className="text-sm text-green-600 mt-2">
                    ✓ Selfie captured/uploaded successfully
                  </p>
                )}
              </div>
              <div className="bg-green-100 rounded-md p-3 mt-4">
                <div className="flex items-start space-x-2">
                  <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center mt-0.5">
                    <span className="text-white text-xs">💡</span>
                  </div>
                  <p className="text-xs text-green-800">
                    <strong>Tips:</strong> Ensure good lighting, remove
                    glasses/hat, and look directly at the camera
                  </p>
                </div>
              </div>
            </div>

            {/* Voice Verification */}
            <div className="bg-purple-50 rounded-lg p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                  <span className="text-white text-sm">🎤</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    Voice Verification
                  </h3>
                  <p className="text-sm text-gray-600">
                    Record a short voice message to prove you're a real person
                  </p>
                </div>
              </div>
              <div className="text-center">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-12 mb-4 hover:border-purple-500 cursor-pointer bg-white">
                  <div className="text-6xl text-gray-400 mb-4">🎤</div>
                  <h4 className="font-medium text-gray-900 mb-2">
                    Record Voice Message
                  </h4>
                  <p className="text-sm text-gray-600 mb-4">
                    "
                    {requirements?.verification_phrase ||
                      "I am verifying my identity for EbaDollar account access"}
                    "
                  </p>
                  <p className="text-xs text-gray-500 mb-4">
                    Please read the phrase above clearly
                  </p>
                  {voiceFile && (
                    <p className="text-sm text-green-600 mb-2">
                      ✓ Voice message recorded
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <div className="bg-white rounded-lg p-4 border">
                    <ReactMic
                      record={isVoiceRecording}
                      className="w-full"
                      onStop={onVoiceStop}
                      mimeType="audio/webm"
                      strokeColor="#6D28D9"
                      backgroundColor="#F3E8FF"
                    />
                  </div>
                  <InteractiveButton
                    className={`!px-8 !py-3 !text-white ${isVoiceRecording ? "!bg-red-500 hover:!bg-red-600" : "!bg-purple-500 hover:!bg-purple-600"}`}
                    onClick={() => setIsVoiceRecording((prev) => !prev)}
                    disabled={uploadingFiles.voice}
                  >
                    {uploadingFiles.voice ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
                        Uploading...
                      </>
                    ) : isVoiceRecording ? (
                      "🛑 Stop Recording"
                    ) : (
                      "🎤 Start Recording"
                    )}
                  </InteractiveButton>
                  <div className="text-sm text-gray-500">or</div>
                  <InteractiveButton
                    className="!px-8 !py-3 !text-purple-600 !bg-white !border !border-purple-600 hover:!bg-purple-50"
                    onClick={() => voiceInputRef.current?.click()}
                    disabled={uploadingFiles.voice || isVoiceRecording}
                  >
                    📁 Upload Audio File
                  </InteractiveButton>
                  <input
                    ref={voiceInputRef}
                    type="file"
                    accept="audio/*"
                    onChange={handleVoiceFileUpload}
                    className="hidden"
                  />
                </div>
              </div>
              <div className="bg-purple-100 rounded-md p-3 mt-4">
                <div className="flex items-start space-x-2">
                  <div className="w-4 h-4 bg-purple-500 rounded-full flex items-center justify-center mt-0.5">
                    <span className="text-white text-xs">🔒</span>
                  </div>
                  <p className="text-xs text-purple-800">
                    <strong>Security:</strong> Voice verification helps prevent
                    automated accounts and ensures real human interaction
                  </p>
                </div>
              </div>
            </div>

            {/* Verification Processing Time */}
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center mt-0.5">
                  <span className="text-white text-xs">ℹ</span>
                </div>
                <div>
                  <h4 className="font-medium text-blue-900 mb-1">
                    Verification Processing Time
                  </h4>
                  <p className="text-sm text-blue-700">
                    Your verification will be processed within 24 hours. You'll
                    receive an email notification once approved.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between items-center pt-8 mt-8 border-t border-gray-200">
            <button
              type="button"
              className="px-6 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E63946]"
              onClick={handleBack}
            >
              Back
            </button>

            <div className="flex space-x-3">
              <button
                type="button"
                className="px-6 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E63946]"
                onClick={handleSkipForNow}
              >
                Skip for Now
              </button>

              <InteractiveButton
                className="!bg-[#E63946] hover:!bg-[#d63384] focus:!ring-[#E63946] !px-6 !py-3 !font-medium"
                loading={submitLoading}
                disabled={submitLoading}
                onClick={handleCompleteVerification}
              >
                Complete Verification
              </InteractiveButton>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
};

export default MemberVerifyIdentityPage;
